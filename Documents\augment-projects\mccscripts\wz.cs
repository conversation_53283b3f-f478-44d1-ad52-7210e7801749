//MCCScript 1.0

MCC.LoadBot(new BoxAreaMonitor());

//MCCScript Extensions

public class BoxAreaMonitor : ChatBot
{
    // Define the monitored box area using the coordinates provided
    // Bottom layer (Y=83): (18,83,-113), (-18,83,-113), (-18,83,-78), (18,83,-78)
    // Top layer (Y=108): (18,108,-113), (-18,108,-113), (-18,108,-78), (18,108,-78)
    // X: -18 to 18
    // Y: 83 to 108
    // Z: -113 to -78
    private readonly Location areaMin = new Location(-18, 83, -113);
    private readonly Location areaMax = new Location(18, 108, -78);

    // How often to check for players (in milliseconds)
    private readonly int checkInterval = 1000;

    // Cooldown period to avoid spam (in seconds)
    private int detectionCooldown = 10;

    // Track players who are in the area along with their info
    private Dictionary<string, PlayerInfo> playersInArea = new Dictionary<string, PlayerInfo>();

    // Track players outside the area to cache their equipment
    private Dictionary<string, PlayerInfo> playersNearby = new Dictionary<string, PlayerInfo>();

    // Track players on cooldown to avoid spam
    private Dictionary<string, DateTime> playerCooldowns = new Dictionary<string, DateTime>();

    // Track when we last checked
    private DateTime lastCheckTime = DateTime.MinValue;

    // Define the extended monitoring area (larger than the box area)
    private Location extendedAreaMin;
    private Location extendedAreaMax;

    // How long to keep player data in cache (in seconds)
    private readonly int playerCacheTime = 300; // 5 minutes

    // Sound alert when players enter the area
    private bool enableSoundAlerts = true;

    // Log to file option
    private bool logToFile = true;
    private string logFilePath = "box_monitor_log.txt";

    // Discord webhook option
    private bool sendToDiscord = true;
    private string discordWebhookUrl = "";
    private string configFilePath = "box_monitor_config.json";

    // Player blacklist - don't send webhooks for these players
    private List<string> playerBlacklist = new List<string>
    {
        "Stwas",
        "SirPenguin",
        "BillTheStallion",
        "Cerv",
        "mcjeep",
        "Wattefakman",
        "IzNoGoud",
        "youy0u",
        "720_",
        "cthan",
        "pac96",
        "swaggybuby",
        "IWannaEat_",
        "Glitchyz",
        "Aviations",
        "CMaac",
        "PNN21",
        "_TicTic",
        "mimsa",
        "kSoren",
        "Aquaablues",
        "TheMethodUE",
        "kongpaochickenFluffy"
    };

    public override void Initialize()
    {
        // Initialize the extended area (50 blocks larger in each direction)
        extendedAreaMin = new Location(areaMin.X - 50, areaMin.Y - 50, areaMin.Z - 50);
        extendedAreaMax = new Location(areaMax.X + 50, areaMax.Y + 50, areaMax.Z + 50);

        // Load configuration
        LoadConfig();

        // Display area information
        LogToConsole($"=== Box Area Monitor Initialized ===");
        LogToConsole($"Monitoring box area with coordinates:");
        LogToConsole($"X: {areaMin.X} to {areaMax.X}");
        LogToConsole($"Y: {areaMin.Y} to {areaMax.Y}");
        LogToConsole($"Z: {areaMin.Z} to {areaMax.Z}");
        LogToConsole($"Extended monitoring area: X: {extendedAreaMin.X} to {extendedAreaMax.X}, Y: {extendedAreaMin.Y} to {extendedAreaMax.Y}, Z: {extendedAreaMin.Z} to {extendedAreaMax.Z}");
        LogToConsole($"Detection cooldown: {detectionCooldown} seconds");

        if (sendToDiscord && !string.IsNullOrEmpty(discordWebhookUrl))
        {
            LogToConsole("Discord notifications: Enabled");
        }
        else if (sendToDiscord)
        {
            LogToConsole("Discord notifications: Disabled (webhook URL not set)");
            sendToDiscord = false;
        }
        else
        {
            LogToConsole("Discord notifications: Disabled");
        }

        // Check if terrain handling is enabled
        if (!GetTerrainEnabled())
        {
            LogToConsole("WARNING: Terrain handling is disabled!");
            LogToConsole("Player tracking may not work correctly.");
            LogToConsole("Please enable 'TerrainAndMovements=true' in MinecraftClient.ini");
        }

        // Check if entity handling is enabled
        if (!GetEntityHandlingEnabled())
        {
            LogToConsole("WARNING: Entity handling is disabled!");
            LogToConsole("Player equipment detection will not work correctly.");
            LogToConsole("Please enable 'EntityHandling=true' in MinecraftClient.ini");
        }

        // Initialize log file if enabled
        if (logToFile)
        {
            try
            {
                // Create or append to log file with header
                File.AppendAllText(logFilePath,
                    $"\n\n=== Box Area Monitor Session Started at {DateTime.Now} ===\n" +
                    $"Monitoring box area with coordinates:\n" +
                    $"X: {areaMin.X} to {areaMax.X}\n" +
                    $"Y: {areaMin.Y} to {areaMax.Y}\n" +
                    $"Z: {areaMin.Z} to {areaMax.Z}\n\n");
            }
            catch (Exception e)
            {
                LogToConsole($"Error creating log file: {e.Message}");
                logToFile = false;
            }
        }
    }

    private void LoadConfig()
    {
        try
        {
            // Check if config file exists
            if (File.Exists(configFilePath))
            {
                // Read the config file line by line
                string[] lines = File.ReadAllLines(configFilePath);

                foreach (string line in lines)
                {
                    // Skip comments and empty lines
                    if (string.IsNullOrWhiteSpace(line) || line.TrimStart().StartsWith("#"))
                        continue;

                    // Split by = to get key-value pairs
                    int equalsPos = line.IndexOf('=');
                    if (equalsPos > 0)
                    {
                        string key = line.Substring(0, equalsPos).Trim();
                        string value = line.Substring(equalsPos + 1).Trim();

                        // Apply settings based on key
                        switch (key.ToLower())
                        {
                            case "discordwebhookurl":
                                discordWebhookUrl = value;
                                break;

                            case "sendtodiscord":
                                bool.TryParse(value, out sendToDiscord);
                                break;

                            case "enablesoundalerts":
                                bool.TryParse(value, out enableSoundAlerts);
                                break;

                            case "logtofile":
                                bool.TryParse(value, out logToFile);
                                break;

                            case "detectioncooldown":
                                int.TryParse(value, out detectionCooldown);
                                break;
                        }
                    }
                }

                LogToConsole("Configuration loaded from file.");
            }
            else
            {
                // Create default config file
                List<string> configLines = new List<string>
                {
                    "# Box Area Monitor Configuration",
                    "# Edit this file to configure the script",
                    "",
                    "# Discord webhook URL (required for Discord notifications)",
                    "DiscordWebhookUrl=",
                    "",
                    "# Enable or disable Discord notifications (true/false)",
                    "SendToDiscord=true",
                    "",
                    "# Enable or disable sound alerts (true/false)",
                    "EnableSoundAlerts=true",
                    "",
                    "# Enable or disable logging to file (true/false)",
                    "LogToFile=true",
                    "",
                    "# Detection cooldown in seconds",
                    $"DetectionCooldown={detectionCooldown}"
                };

                File.WriteAllLines(configFilePath, configLines);

                LogToConsole("Default configuration file created. Please edit it to set your Discord webhook URL.");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error loading configuration: {ex.Message}");
            LogToConsole("Using default settings.");
        }
    }

    private void SaveConfig()
    {
        try
        {
            // Create config lines
            List<string> configLines = new List<string>
            {
                "# Box Area Monitor Configuration",
                "# Edit this file to configure the script",
                "",
                "# Discord webhook URL (required for Discord notifications)",
                $"DiscordWebhookUrl={discordWebhookUrl}",
                "",
                "# Enable or disable Discord notifications (true/false)",
                $"SendToDiscord={sendToDiscord}",
                "",
                "# Enable or disable sound alerts (true/false)",
                $"EnableSoundAlerts={enableSoundAlerts}",
                "",
                "# Enable or disable logging to file (true/false)",
                $"LogToFile={logToFile}",
                "",
                "# Detection cooldown in seconds",
                $"DetectionCooldown={detectionCooldown}"
            };

            // Save to file
            File.WriteAllLines(configFilePath, configLines);

            LogToConsole("Configuration saved.");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error saving configuration: {ex.Message}");
        }
    }

    // Override OnEntityEquipment to capture equipment updates
    public override void OnEntityEquipment(Entity entity, int slot, Item? item)
    {
        try
        {
            // Silently process equipment updates
            if (entity != null && entity.Type == EntityType.Player && !string.IsNullOrEmpty(entity.Name))
            {
                // If this player is in our monitored area, update their equipment info
                if (playersInArea.ContainsKey(entity.Name))
                {
                    // Get current player info
                    PlayerInfo playerInfo = playersInArea[entity.Name];

                    // Update the specific equipment slot
                    Dictionary<int, Item> equipment = GetPlayerEquipment(entity);
                    ArmorInfo armorInfo = AnalyzeArmor(equipment);
                    playerInfo.ArmorInfo = armorInfo;

                    // Update player info
                    playersInArea[entity.Name] = playerInfo;
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error in OnEntityEquipment: {ex.Message}");
        }
    }

    public override void Update()
    {
        // Only check at the specified interval
        if ((DateTime.Now - lastCheckTime).TotalMilliseconds < checkInterval)
            return;

        lastCheckTime = DateTime.Now;

        // Get world data
        World world = GetWorld();
        if (world == null)
            return;

        // Get all entities directly from ChatBot
        Dictionary<int, Entity> entities = GetEntities();

        // Track which players are currently in the area
        HashSet<string> currentPlayersInArea = new HashSet<string>();

        // Track which players are currently in the extended area
        HashSet<string> currentPlayersInExtendedArea = new HashSet<string>();

        // Clean up old players from nearby cache
        List<string> playersToRemoveFromNearby = new List<string>();
        foreach (var player in playersNearby)
        {
            // Remove players that haven't been seen for a while
            if ((DateTime.Now - player.Value.LastEquipmentCheck).TotalSeconds > playerCacheTime)
            {
                playersToRemoveFromNearby.Add(player.Key);
            }
        }

        // Remove old players from nearby cache
        foreach (string player in playersToRemoveFromNearby)
        {
            playersNearby.Remove(player);
        }

        // Check each entity
        foreach (var entity in entities.Values)
        {
            // Only interested in players
            if (entity.Type != EntityType.Player || string.IsNullOrEmpty(entity.Name))
                continue;

            // Skip if it's the client player
            if (entity.Name == GetUsername())
                continue;

            // Check if player is in the extended monitoring area
            if (IsLocationInExtendedArea(entity.Location))
            {
                currentPlayersInExtendedArea.Add(entity.Name);

                // Get player's armor information
                Dictionary<int, Item> equipment = GetPlayerEquipment(entity);
                ArmorInfo armorInfo = AnalyzeArmor(equipment);

                // If player is not in the main area but in the extended area
                if (!IsLocationInArea(entity.Location))
                {
                    // Update or add to nearby players cache
                    if (playersNearby.ContainsKey(entity.Name))
                    {
                        // Update existing player info
                        PlayerInfo playerInfo = playersNearby[entity.Name];
                        playerInfo.LastPosition = entity.Location;
                        playerInfo.LastEquipmentCheck = DateTime.Now;
                        playerInfo.ArmorInfo = armorInfo;
                        playersNearby[entity.Name] = playerInfo;
                    }
                    else
                    {
                        // Add new player to nearby cache
                        PlayerInfo playerInfo = new PlayerInfo
                        {
                            EnteredTime = DateTime.Now,
                            LastPosition = entity.Location,
                            ArmorInfo = armorInfo,
                            EquipmentChecked = true,
                            LastEquipmentCheck = DateTime.Now
                        };

                        playersNearby[entity.Name] = playerInfo;
                        // Silent tracking - no logging to reduce spam
                    }
                }

                // Check if player is in the main monitored area
                if (IsLocationInArea(entity.Location))
                {
                    currentPlayersInArea.Add(entity.Name);

                    // Check if player is on cooldown
                    bool isOnCooldown = false;
                    if (playerCooldowns.ContainsKey(entity.Name))
                    {
                        TimeSpan timeSinceCooldown = DateTime.Now - playerCooldowns[entity.Name];
                        if (timeSinceCooldown.TotalSeconds < detectionCooldown)
                        {
                            isOnCooldown = true;
                        }
                        else
                        {
                            // Cooldown expired, remove from cooldown list
                            playerCooldowns.Remove(entity.Name);
                        }
                    }

                    // If this is a new player in the area and not on cooldown
                    if (!playersInArea.ContainsKey(entity.Name) && !isOnCooldown)
                    {
                        // Check if we already have this player's info in the nearby cache
                        if (playersNearby.ContainsKey(entity.Name))
                        {
                            // Use the cached player info
                            PlayerInfo playerInfo = playersNearby[entity.Name];
                            playerInfo.EnteredTime = DateTime.Now;
                            playerInfo.LastPosition = entity.Location;
                            playerInfo.LastEquipmentCheck = DateTime.Now;

                            // Add to players in area
                            playersInArea[entity.Name] = playerInfo;

                            // Silent tracking - no logging to reduce spam

                            // Trigger the player entered area event
                            OnPlayerEnteredArea(entity, playerInfo.ArmorInfo);
                        }
                        else
                        {
                            // Create new player info
                            PlayerInfo playerInfo = new PlayerInfo
                            {
                                EnteredTime = DateTime.Now,
                                LastPosition = entity.Location,
                                ArmorInfo = armorInfo,
                                EquipmentChecked = false,
                                LastEquipmentCheck = DateTime.Now
                            };

                            playersInArea[entity.Name] = playerInfo;
                            OnPlayerEnteredArea(entity, armorInfo);
                        }

                        // Add player to cooldown list
                        playerCooldowns[entity.Name] = DateTime.Now;
                    }
                    else if (playersInArea.ContainsKey(entity.Name))
                    {
                        // Update the player info without triggering alerts
                        PlayerInfo playerInfo = playersInArea[entity.Name];
                        playerInfo.LastPosition = entity.Location;

                        // Check if we need to update equipment
                        bool updateEquipment = false;

                        // If equipment hasn't been fully checked yet
                        if (!playerInfo.EquipmentChecked)
                        {
                            // Check if boots or main hand are missing
                            bool missingEquipment =
                                playerInfo.ArmorInfo.Boots == "None" ||
                                playerInfo.ArmorInfo.Boots.Contains("Not visible") ||
                                playerInfo.ArmorInfo.MainHand == "None" ||
                                playerInfo.ArmorInfo.MainHand.Contains("Not visible");

                            // If missing equipment and it's been at least 2 seconds since last check
                            if (missingEquipment && (DateTime.Now - playerInfo.LastEquipmentCheck).TotalSeconds >= 2)
                            {
                                updateEquipment = true;
                                playerInfo.LastEquipmentCheck = DateTime.Now;

                                // If we've checked 3 times and still missing equipment, mark as checked
                                if ((DateTime.Now - playerInfo.EnteredTime).TotalSeconds >= 6)
                                {
                                    playerInfo.EquipmentChecked = true;
                                }
                            }
                        }

                        // Update equipment if needed
                        if (updateEquipment)
                        {
                            // Get fresh equipment data
                            Dictionary<int, Item> freshEquipment = GetPlayerEquipment(entity);
                            ArmorInfo freshArmorInfo = AnalyzeArmor(freshEquipment);

                            // Only update if we found new equipment
                            if ((playerInfo.ArmorInfo.Boots == "None" || playerInfo.ArmorInfo.Boots.Contains("Not visible")) &&
                                freshArmorInfo.Boots != "None" && !freshArmorInfo.Boots.Contains("Not visible"))
                            {
                                playerInfo.ArmorInfo.Boots = freshArmorInfo.Boots;
                                playerInfo.ArmorInfo.BootsItem = freshArmorInfo.BootsItem;
                            }

                            if ((playerInfo.ArmorInfo.MainHand == "None" || playerInfo.ArmorInfo.MainHand.Contains("Not visible")) &&
                                freshArmorInfo.MainHand != "None" && !freshArmorInfo.MainHand.Contains("Not visible"))
                            {
                                playerInfo.ArmorInfo.MainHand = freshArmorInfo.MainHand;
                                playerInfo.ArmorInfo.MainHandItem = freshArmorInfo.MainHandItem;
                            }

                            // Log updated equipment
                            LogToConsole($"Updated equipment for {entity.Name}:");
                            LogArmorInfo(entity.Name, playerInfo.ArmorInfo);
                        }
                        else
                        {
                            // Just update with current armor info
                            playerInfo.ArmorInfo = armorInfo;
                        }

                        playersInArea[entity.Name] = playerInfo;
                    }
                }
            }
        }

        // Check for players who left the area
        List<string> playersWhoLeft = new List<string>();
        foreach (var player in playersInArea)
        {
            if (!currentPlayersInArea.Contains(player.Key))
            {
                // Player is no longer in the area
                playersWhoLeft.Add(player.Key);
                OnPlayerLeftArea(player.Key, player.Value);

                // If player is still in extended area, add to nearby cache
                if (currentPlayersInExtendedArea.Contains(player.Key) && !playersNearby.ContainsKey(player.Key))
                {
                    playersNearby[player.Key] = player.Value;
                    // Silent tracking - no logging to reduce spam
                }
            }
        }

        // Remove players who left from tracking
        foreach (string player in playersWhoLeft)
        {
            playersInArea.Remove(player);
        }

        // Silent tracking - no logging to reduce spam
    }

    private Dictionary<int, Item> GetPlayerEquipment(Entity player)
    {
        // Try to get player equipment from entity data
        Dictionary<int, Item> equipment = new Dictionary<int, Item>();

        // Check if the entity has equipment data
        if (player.Equipment != null)
        {
            // Silent equipment processing - no logging to reduce spam
            return player.Equipment;
        }

        // Silent equipment processing - no logging to reduce spam
        return equipment;
    }

    private void LogSlotInfo(Dictionary<int, Item> equipment, int slot, string description)
    {
        // Silent slot info - no logging to reduce spam
    }

    private string GetItemName(Item item)
    {
        if (item == null)
            return "null";

        // Check if the item has a custom display name
        if (!string.IsNullOrEmpty(item.DisplayName))
        {
            string itemName = item.DisplayName;

            // Add lore lines if available
            if (item.Lores != null && item.Lores.Length > 0)
            {
                itemName += " (";
                for (int i = 0; i < Math.Min(2, item.Lores.Length); i++)
                {
                    if (i > 0) itemName += ", ";
                    itemName += item.Lores[i];
                }
                if (item.Lores.Length > 2)
                    itemName += "...";
                itemName += ")";
            }

            return itemName;
        }

        // Check if the item has NBT data (enchantments, etc.)
        string itemInfo = item.Type.ToString();

        if (item.NBT != null)
        {
            // Try to extract enchantment information
            try
            {
                if (item.NBT.ContainsKey("Enchantments"))
                {
                    itemInfo += " (Enchanted)";
                }

                // Add damage/durability info if available
                if (item.NBT.ContainsKey("Damage"))
                {
                    itemInfo += $" [Damage: {item.NBT["Damage"]}]";
                }
            }
            catch
            {
                // If we can't parse the NBT data, just return the type
            }
        }

        // Add lore lines if available
        if (item.Lores != null && item.Lores.Length > 0)
        {
            itemInfo += " (";
            for (int i = 0; i < Math.Min(2, item.Lores.Length); i++)
            {
                if (i > 0) itemInfo += ", ";
                itemInfo += item.Lores[i];
            }
            if (item.Lores.Length > 2)
                itemInfo += "...";
            itemInfo += ")";
        }

        return itemInfo;
    }

    private ArmorInfo AnalyzeArmor(Dictionary<int, Item> equipment)
    {
        ArmorInfo armorInfo = new ArmorInfo();

        try
        {
            // Silently process equipment data without logging
            // Based on observed data and equipment update logs:
            // 0: Main Hand
            // 1: Boots (confirmed)
            // 2: Helmet (sometimes also shows pickaxe)
            // 64: Off Hand (TotemOfUndying) or sometimes Sword
            // 65: Leggings
            // 66: Chestplate

            // First pass: Assign the slots we know
            foreach (var entry in equipment)
            {
                int slot = entry.Key;
                Item item = entry.Value;

                if (item == null)
                    continue;

                string itemName = GetItemName(item);

                // Assign based on observed patterns
                if (slot == 1)
                {
                    // Slot 1 is confirmed to be boots
                    armorInfo.Boots = itemName;
                    armorInfo.BootsItem = item;
                }
                else if (slot == 2)
                {
                    // Slot 2 is usually helmet, but sometimes shows pickaxe
                    if (itemName.Contains("Helmet") || !itemName.Contains("Pickaxe"))
                    {
                        armorInfo.Helmet = itemName;
                        armorInfo.HelmetItem = item;
                    }
                }
                else if (slot == 66)
                {
                    // Slot 66 is chestplate
                    armorInfo.Chestplate = itemName;
                    armorInfo.ChestplateItem = item;
                }
                else if (slot == 65)
                {
                    // Slot 65 is leggings
                    armorInfo.Leggings = itemName;
                    armorInfo.LeggingsItem = item;
                }
                else if (slot == 64)
                {
                    // Slot 64 is usually off hand, but can be sword
                    if (itemName.Contains("Sword") || itemName.Contains("Axe") ||
                        itemName.Contains("Pickaxe") || itemName.Contains("Shovel") ||
                        itemName.Contains("Hoe") || itemName.Contains("Bow"))
                    {
                        armorInfo.MainHand = itemName;
                        armorInfo.MainHandItem = item;
                    }
                    else
                    {
                        armorInfo.OffHand = itemName;
                        armorInfo.OffHandItem = item;
                    }
                }
                else if (slot == 0)
                {
                    // Slot 0 is main hand
                    armorInfo.MainHand = itemName;
                    armorInfo.MainHandItem = item;
                }
                // Check other potential boot slots as fallback
                else if (slot == 3 || slot == 63 || slot == 67)
                {
                    // Only use these slots if boots haven't been found yet
                    if (armorInfo.Boots == "None")
                    {
                        armorInfo.Boots = itemName;
                        armorInfo.BootsItem = item;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error analyzing equipment: {ex.Message}");
        }

        try
        {
            // Check if we're missing boots but have a player name
            if (armorInfo.Boots == "None" && equipment.Count > 0)
            {
                // Try to get the player's name from the first entity we find
                string playerName = "";
                Entity playerEntity = null;

                var entities = GetEntities();
                if (entities != null)
                {
                    foreach (var entity in entities.Values)
                    {
                        if (entity != null && entity.Type == EntityType.Player &&
                            entity.Name != null && playersInArea.ContainsKey(entity.Name))
                        {
                            playerName = entity.Name;
                            playerEntity = entity;
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(playerName) && playerEntity != null && playerEntity.Equipment != null)
                {
                    // Log all available slots for this entity
                    LogToConsole($"All equipment slots for {playerName}: {string.Join(", ", playerEntity.Equipment.Keys)}");

                    // Try a few more potential boot slots
                    int[] potentialBootSlots = { 1, 3, 63, 67, 68, 69, 70, 71, 72, 73, 74, 75 };
                    foreach (int slot in potentialBootSlots)
                    {
                        if (playerEntity.Equipment.ContainsKey(slot) && playerEntity.Equipment[slot] != null)
                        {
                            string itemName = GetItemName(playerEntity.Equipment[slot]);
                            LogToConsole($"Found item in potential boot slot {slot}: {itemName}");

                            if (itemName.Contains("Boots") || itemName.Contains("Shoes") || itemName.Contains("Feet"))
                            {
                                armorInfo.Boots = itemName;
                                LogToConsole($"Identified boots in slot {slot}: {itemName}");
                                break;
                            }
                        }
                    }

                    // If we still don't have boots, use a hardcoded value for now
                    if (armorInfo.Boots == "None")
                    {
                        armorInfo.Boots = "[Not visible in equipment data]";
                    }

                    // We're no longer logging equipment slot analysis
                    // Just silently process the equipment data
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error finding boots: {ex.Message}");
        }

        try
        {
            // Second pass: Try to identify items by their names if slots didn't work
            foreach (var entry in equipment)
            {
                Item item = entry.Value;
                if (item == null)
                    continue;

                string itemName = GetItemName(item);

                // Assign items to the appropriate slots based on name patterns if not already assigned
                if (armorInfo.Helmet == "None" && (itemName.Contains("Helmet") || itemName.Contains("Cap") || itemName.Contains("Hat")))
                    armorInfo.Helmet = itemName;

                if (armorInfo.Chestplate == "None" && (itemName.Contains("Chestplate") || itemName.Contains("Tunic") ||
                                                      itemName.Contains("Warrior") || itemName.Contains("Chest")))
                    armorInfo.Chestplate = itemName;

                if (armorInfo.Leggings == "None" && (itemName.Contains("Leggings") || itemName.Contains("Pants")))
                    armorInfo.Leggings = itemName;

                if (armorInfo.Boots == "None" && (itemName.Contains("Boots") || itemName.Contains("Shoes") || itemName.Contains("Feet")))
                    armorInfo.Boots = itemName;

                if (armorInfo.OffHand == "None" && (itemName.Contains("Totem") || itemName.Contains("Shield")))
                    armorInfo.OffHand = itemName;

                if (armorInfo.MainHand == "None" && (itemName.Contains("Sword") || itemName.Contains("Axe") ||
                                                   itemName.Contains("Pickaxe") || itemName.Contains("Shovel") ||
                                                   itemName.Contains("Hoe") || itemName.Contains("Bow") ||
                                                   itemName.Contains("Trident")))
                    armorInfo.MainHand = itemName;
            }

            // If we still don't have main hand information, just note it's missing
            if (armorInfo.MainHand == "None")
            {
                armorInfo.MainHand = "[Not visible in equipment data]";
            }

            // If we still don't have boots information, just note it's missing
            if (armorInfo.Boots == "None")
            {
                armorInfo.Boots = "[Not visible in equipment data]";
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error in second pass: {ex.Message}");
        }

        // We're no longer calculating armor rating
        armorInfo.ArmorRating = 0;

        return armorInfo;
    }

    private int CalculateArmorRating(ArmorInfo armor)
    {
        int rating = 0;

        // Add bonus for enchanted items
        if (armor.Helmet.Contains("Enchanted")) rating += 1;
        if (armor.Chestplate.Contains("Enchanted")) rating += 1;
        if (armor.Leggings.Contains("Enchanted")) rating += 1;
        if (armor.Boots.Contains("Enchanted")) rating += 1;

        // Add bonus for custom items (marked with ★)
        if (armor.Helmet.Contains("★")) rating += 3;
        if (armor.Chestplate.Contains("★")) rating += 3;
        if (armor.Leggings.Contains("★")) rating += 3;
        if (armor.Boots.Contains("★")) rating += 3;

        // If we already added points for custom items, skip the standard ratings
        if (!armor.Helmet.Contains("★"))
        {
            // Helmet ratings
            if (armor.Helmet.Contains("Netherite")) rating += 3;
            else if (armor.Helmet.Contains("Diamond")) rating += 3;
            else if (armor.Helmet.Contains("Iron")) rating += 2;
            else if (armor.Helmet.Contains("Golden") || armor.Helmet.Contains("Chain")) rating += 2;
            else if (armor.Helmet.Contains("Leather")) rating += 1;
        }

        if (!armor.Chestplate.Contains("★"))
        {
            // Chestplate ratings
            if (armor.Chestplate.Contains("Netherite")) rating += 8;
            else if (armor.Chestplate.Contains("Diamond")) rating += 8;
            else if (armor.Chestplate.Contains("Iron")) rating += 6;
            else if (armor.Chestplate.Contains("Golden") || armor.Chestplate.Contains("Chain")) rating += 5;
            else if (armor.Chestplate.Contains("Leather")) rating += 3;
        }

        if (!armor.Leggings.Contains("★"))
        {
            // Leggings ratings
            if (armor.Leggings.Contains("Netherite")) rating += 6;
            else if (armor.Leggings.Contains("Diamond")) rating += 6;
            else if (armor.Leggings.Contains("Iron")) rating += 5;
            else if (armor.Leggings.Contains("Golden") || armor.Leggings.Contains("Chain")) rating += 4;
            else if (armor.Leggings.Contains("Leather")) rating += 2;
        }

        if (!armor.Boots.Contains("★"))
        {
            // Boots ratings
            if (armor.Boots.Contains("Netherite")) rating += 3;
            else if (armor.Boots.Contains("Diamond")) rating += 3;
            else if (armor.Boots.Contains("Iron")) rating += 2;
            else if (armor.Boots.Contains("Golden") || armor.Boots.Contains("Chain")) rating += 1;
            else if (armor.Boots.Contains("Leather")) rating += 1;
        }

        return rating;
    }

    private bool IsLocationInArea(Location loc)
    {
        return loc.X >= areaMin.X && loc.X <= areaMax.X &&
               loc.Y >= areaMin.Y && loc.Y <= areaMax.Y &&
               loc.Z >= areaMin.Z && loc.Z <= areaMax.Z;
    }

    private bool IsLocationInExtendedArea(Location loc)
    {
        return loc.X >= extendedAreaMin.X && loc.X <= extendedAreaMax.X &&
               loc.Y >= extendedAreaMin.Y && loc.Y <= extendedAreaMax.Y &&
               loc.Z >= extendedAreaMin.Z && loc.Z <= extendedAreaMax.Z;
    }

    private void OnPlayerEnteredArea(Entity player, ArmorInfo armorInfo)
    {
        // Check if player is in the blacklist
        if (playerBlacklist.Contains(player.Name))
        {
            LogToConsole($"Player {player.Name} is in the blacklist. Skipping alerts and webhooks.");
            return;
        }

        // Log all equipment for debugging
        LogToConsole($"PLAYER ENTERED AREA: {player.Name}");
        LogToConsole($"EQUIPMENT CHECK:");

        // Log all equipment pieces
        if (armorInfo.HelmetItem != null)
            LogToConsole($"Helmet: {armorInfo.HelmetItem.DisplayName}");
        if (armorInfo.ChestplateItem != null)
            LogToConsole($"Chestplate: {armorInfo.ChestplateItem.DisplayName}");
        if (armorInfo.LeggingsItem != null)
            LogToConsole($"Leggings: {armorInfo.LeggingsItem.DisplayName}");
        if (armorInfo.BootsItem != null)
            LogToConsole($"Boots: {armorInfo.BootsItem.DisplayName}");
        if (armorInfo.MainHandItem != null)
            LogToConsole($"Main Hand: {armorInfo.MainHandItem.DisplayName}");
        if (armorInfo.OffHandItem != null)
            LogToConsole($"Off Hand: {armorInfo.OffHandItem.DisplayName}");

        // Check if the player has netherite or high-tier equipment
        if (!HasNetheriteOrHighTier(armorInfo))
        {
            LogToConsole($"Player {player.Name} does not have netherite or high-tier equipment. Skipping alerts.");
            return;
        }

        // Check for Godlike equipment
        if (HasGodlikeEquipment(armorInfo))
        {
            LogToConsole($"Player {player.Name} has Godlike equipment. Sending webhook.");
        }

        string message = $"ALERT: Player {player.Name} detected in the box area at {DateTime.Now}";
        LogToConsole(message);

        // Get player position for more detailed logging
        Location playerPos = player.Location;
        string detailedMessage = $"Position: X:{playerPos.X:F1}, Y:{playerPos.Y:F1}, Z:{playerPos.Z:F1}";
        LogToConsole(detailedMessage);

        // Play alert sound
        if (enableSoundAlerts)
        {
            for (int i = 0; i < 3; i++)
            {
                Console.Beep(880, 200);
                Thread.Sleep(100);
            }
        }

        // Send to Discord webhook
        SendToDiscord(player.Name, playerPos, armorInfo);

        // Log to file
        if (logToFile)
        {
            try
            {
                File.AppendAllText(logFilePath,
                    $"[{DateTime.Now}] DETECTED: {player.Name} at X:{playerPos.X:F1}, Y:{playerPos.Y:F1}, Z:{playerPos.Z:F1}\n" +
                    $"  Armor: {GetArmorSummary(armorInfo)}\n");
            }
            catch { /* Ignore file write errors */ }
        }
    }

    private void LogArmorInfo(string playerName, ArmorInfo armor)
    {
        // Silent equipment logging - no logging to reduce spam
    }

    private string AssessThreatLevel(ArmorInfo armor)
    {
        // Calculate a more accurate armor rating that accounts for custom items
        int adjustedArmorRating = armor.ArmorRating;

        // Add bonus points for custom armor pieces (marked with ★)
        if (armor.Helmet.Contains("★")) adjustedArmorRating += 2;
        if (armor.Chestplate.Contains("★")) adjustedArmorRating += 2;
        if (armor.Leggings.Contains("★")) adjustedArmorRating += 2;
        if (armor.Boots.Contains("★")) adjustedArmorRating += 2;

        // Assess threat based on armor rating and weapons
        bool hasHighTierWeapon =
            armor.MainHand.Contains("DiamondSword") ||
            armor.MainHand.Contains("DiamondAxe") ||
            armor.MainHand.Contains("NetheriteSword") ||
            armor.MainHand.Contains("NetheriteAxe") ||
            armor.MainHand.Contains("★");  // Custom weapons are high-tier

        bool hasWeapon =
            armor.MainHand.Contains("Sword") ||
            armor.MainHand.Contains("Axe") ||
            armor.MainHand.Contains("Trident") ||
            armor.MainHand.Contains("★");

        bool hasEnchantedWeapon = hasWeapon &&
            (armor.MainHand.Contains("Enchanted") || armor.MainHand.Contains("★"));

        bool hasTotem = armor.OffHand.Contains("Totem");

        // Assess threat level
        if (adjustedArmorRating >= 15 && hasHighTierWeapon && hasTotem)
            return "VERY HIGH - Full custom armor with high-tier weapon and totem";
        else if (adjustedArmorRating >= 15 && hasHighTierWeapon)
            return "HIGH - Full armor with high-tier weapon";
        else if (adjustedArmorRating >= 15 || (adjustedArmorRating >= 10 && hasEnchantedWeapon))
            return "HIGH - Well equipped with enchantments";
        else if (adjustedArmorRating >= 10 || hasTotem)
            return "MEDIUM - Well armored or has totem";
        else if (adjustedArmorRating > 0 || hasWeapon)
            return "LOW - Partially armored or armed";
        else
            return "MINIMAL - No significant armor or weapons";
    }

    private string GetArmorSummary(ArmorInfo armor)
    {
        List<string> equipment = new List<string>();

        if (armor.Helmet != "None")
            equipment.Add(armor.Helmet);

        if (armor.Chestplate != "None")
            equipment.Add(armor.Chestplate);

        if (armor.Leggings != "None")
            equipment.Add(armor.Leggings);

        if (armor.Boots != "None" && !armor.Boots.Contains("Not visible"))
            equipment.Add(armor.Boots);

        if (armor.MainHand != "None" && !armor.MainHand.Contains("Not visible"))
            equipment.Add(armor.MainHand);

        if (armor.OffHand != "None")
            equipment.Add(armor.OffHand);

        if (equipment.Count == 0)
            return "No equipment";

        return string.Join(", ", equipment);
    }

    private string GetItemWithFullLore(Item item)
    {
        if (item == null)
            return "None";

        string displayName = !string.IsNullOrEmpty(item.DisplayName) ? item.DisplayName : item.Type.ToString();

        // Add full lore
        if (item.Lores != null && item.Lores.Length > 0)
        {
            string result = displayName;

            // Log the lore lines for debugging
            LogToConsole($"Item {displayName} has {item.Lores.Length} lore lines:");
            for (int i = 0; i < item.Lores.Length; i++)
            {
                LogToConsole($"  Lore line {i+1}: {item.Lores[i]}");
            }

            // Add each lore line
            foreach (string lore in item.Lores)
            {
                // Clean up the lore line but keep more information
                string cleanLore = lore.Replace("§", "").Trim();
                if (!string.IsNullOrEmpty(cleanLore))
                {
                    result += "\n" + cleanLore;
                }
            }
            return result;
        }

        return displayName;
    }

    private string CleanTextForDiscord(string text)
    {
        if (string.IsNullOrEmpty(text))
            return text;

        // Remove ALL special characters and keep only basic ASCII
        string cleaned = System.Text.RegularExpressions.Regex.Replace(text, @"[^\x20-\x7E]", "");

        // Replace characters that might cause issues in Discord
        cleaned = cleaned.Replace("\\", "")
                         .Replace("\"", "")
                         .Replace("'", "")
                         .Replace("`", "");

        // Extract the item name and first few enchantments
        int enchantmentIndex = cleaned.IndexOf(" (");
        if (enchantmentIndex > 0)
        {
            string itemName = cleaned.Substring(0, enchantmentIndex);
            string enchantments = cleaned.Substring(enchantmentIndex);

            // Limit enchantments to first two for cleaner display
            int commaIndex = enchantments.IndexOf(",", enchantments.IndexOf(",") + 1);
            if (commaIndex > 0)
            {
                enchantments = enchantments.Substring(0, commaIndex) + "...)";
            }

            cleaned = itemName + enchantments;
        }

        // Remove any remaining special characters
        cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"[^\u0020-\u007E]", "");

        return cleaned;
    }

    private string GetItemEmoji(string itemName)
    {
        // Add emojis based on item type
        if (itemName.Contains("Helmet") || itemName.Contains("Cap") || itemName.Contains("Hat"))
            return "🪖 ";
        else if (itemName.Contains("Chestplate") || itemName.Contains("Tunic") || itemName.Contains("Warrior"))
            return "🛡️ ";
        else if (itemName.Contains("Leggings") || itemName.Contains("Pants"))
            return "👖 ";
        else if (itemName.Contains("Boots") || itemName.Contains("Shoes"))
            return "👢 ";
        else if (itemName.Contains("Sword"))
            return "⚔️ ";
        else if (itemName.Contains("Axe"))
            return "🪓 ";
        else if (itemName.Contains("Bow"))
            return "🏹 ";
        else if (itemName.Contains("Shield"))
            return "🛡️ ";
        else if (itemName.Contains("Totem"))
            return "🧿 ";
        else if (itemName.Contains("Pickaxe"))
            return "⛏️ ";
        else if (itemName.Contains("Shovel"))
            return "🔨 ";
        else if (itemName.Contains("Hoe"))
            return "🌾 ";
        else if (itemName.Contains("Trident"))
            return "🔱 ";
        else
            return "🔮 ";
    }



    private void SendToDiscord(string playerName, Location location, ArmorInfo armor)
    {
        // ABSOLUTE BLOCK FOR MONSTER PLAYERS
        if (playerName.ToLower().Contains("monster"))
        {
            LogToConsole($"ABSOLUTE BLOCK: Player {playerName} is a monster. BLOCKING WEBHOOK.");
            return;
        }

        // Check if the player has netherite or high-tier equipment
        if (!HasNetheriteOrHighTier(armor))
        {
            LogToConsole($"Player {playerName} does not have netherite or high-tier equipment. Skipping webhook.");
            return;
        }

        if (!sendToDiscord || string.IsNullOrEmpty(discordWebhookUrl))
        {
            LogToConsole("Discord notifications are disabled or webhook URL is not set.");
            LogToConsole("Edit the configuration file to set your Discord webhook URL.");
            return;
        }

        // Check if player is in the blacklist
        if (playerBlacklist.Contains(playerName))
        {
            LogToConsole($"Player {playerName} is in the blacklist. Skipping webhook.");
            return;
        }

        // Log all equipment for debugging
        LogToConsole($"FINAL EQUIPMENT CHECK FOR {playerName}:");

        // Display equipment for logging purposes only
        if (armor.Helmet != null)
            LogToConsole($"Helmet: {armor.Helmet}");

        if (armor.Chestplate != null)
            LogToConsole($"Chestplate: {armor.Chestplate}");

        if (armor.Leggings != null)
            LogToConsole($"Leggings: {armor.Leggings}");

        if (armor.Boots != null)
            LogToConsole($"Boots: {armor.Boots}");

        if (armor.MainHand != null)
            LogToConsole($"Main Hand: {armor.MainHand}");

        if (armor.OffHand != null)
            LogToConsole($"Off Hand: {armor.OffHand}");

        // Use the file-based approach
        SendToDiscordViaFile(playerName, location, armor);
        LogToConsole($"Prepared Discord notification for player {playerName}");
    }

    private string FormatEquipmentForDiscord(ArmorInfo armor)
    {
        // Format equipment for Discord with proper formatting
        StringBuilder formattedEquipment = new StringBuilder();

        // Format helmet
        if (armor.HelmetItem != null)
        {
            string itemName = armor.HelmetItem.DisplayName;
            if (string.IsNullOrEmpty(itemName))
                itemName = armor.HelmetItem.Type.ToString();

            // Clean the item name
            string cleanedItemName = CleanLoreLine(itemName);

            formattedEquipment.AppendLine($"**Helmet**: {cleanedItemName}");

            if (armor.HelmetItem.Lores != null && armor.HelmetItem.Lores.Length > 0)
            {
                // Get first two enchantments
                if (armor.HelmetItem.Lores.Length > 0)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.HelmetItem.Lores[0])}");

                if (armor.HelmetItem.Lores.Length > 1)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.HelmetItem.Lores[1])}");

                // Get last enchantment (if it's a Crate Exclusive or different from first two)
                if (armor.HelmetItem.Lores.Length > 2)
                {
                    string lastLore = null;

                    // Look for Crate Exclusive
                    for (int i = 0; i < armor.HelmetItem.Lores.Length; i++)
                    {
                        if (armor.HelmetItem.Lores[i].Contains("Crate Exclusive"))
                        {
                            lastLore = armor.HelmetItem.Lores[i];
                            break;
                        }
                    }

                    // If no Crate Exclusive found, use the last lore line
                    if (lastLore == null)
                        lastLore = armor.HelmetItem.Lores[armor.HelmetItem.Lores.Length - 1];

                    // Only add if different from first two
                    if (armor.HelmetItem.Lores.Length <= 1 ||
                        (lastLore != armor.HelmetItem.Lores[0] && lastLore != armor.HelmetItem.Lores[1]))
                    {
                        formattedEquipment.AppendLine($"> {CleanLoreLine(lastLore)}");
                    }
                }
            }

            formattedEquipment.AppendLine();
        }

        // Format chestplate
        if (armor.ChestplateItem != null)
        {
            string itemName = armor.ChestplateItem.DisplayName;
            if (string.IsNullOrEmpty(itemName))
                itemName = armor.ChestplateItem.Type.ToString();

            // Clean the item name
            string cleanedItemName = CleanLoreLine(itemName);

            formattedEquipment.AppendLine($"**Chestplate**: {cleanedItemName}");

            if (armor.ChestplateItem.Lores != null && armor.ChestplateItem.Lores.Length > 0)
            {
                // Get first two enchantments
                if (armor.ChestplateItem.Lores.Length > 0)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.ChestplateItem.Lores[0])}");

                if (armor.ChestplateItem.Lores.Length > 1)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.ChestplateItem.Lores[1])}");

                // Get last enchantment (if it's a Crate Exclusive or different from first two)
                if (armor.ChestplateItem.Lores.Length > 2)
                {
                    string lastLore = null;

                    // Look for Crate Exclusive
                    for (int i = 0; i < armor.ChestplateItem.Lores.Length; i++)
                    {
                        if (armor.ChestplateItem.Lores[i].Contains("Crate Exclusive"))
                        {
                            lastLore = armor.ChestplateItem.Lores[i];
                            break;
                        }
                    }

                    // If no Crate Exclusive found, use the last lore line
                    if (lastLore == null)
                        lastLore = armor.ChestplateItem.Lores[armor.ChestplateItem.Lores.Length - 1];

                    // Only add if different from first two
                    if (armor.ChestplateItem.Lores.Length <= 1 ||
                        (lastLore != armor.ChestplateItem.Lores[0] && lastLore != armor.ChestplateItem.Lores[1]))
                    {
                        formattedEquipment.AppendLine($"> {CleanLoreLine(lastLore)}");
                    }
                }
            }

            formattedEquipment.AppendLine();
        }

        // Format leggings
        if (armor.LeggingsItem != null)
        {
            string itemName = armor.LeggingsItem.DisplayName;
            if (string.IsNullOrEmpty(itemName))
                itemName = armor.LeggingsItem.Type.ToString();

            // Clean the item name
            string cleanedItemName = CleanLoreLine(itemName);

            formattedEquipment.AppendLine($"**Leggings**: {cleanedItemName}");

            if (armor.LeggingsItem.Lores != null && armor.LeggingsItem.Lores.Length > 0)
            {
                // Get first two enchantments
                if (armor.LeggingsItem.Lores.Length > 0)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.LeggingsItem.Lores[0])}");

                if (armor.LeggingsItem.Lores.Length > 1)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.LeggingsItem.Lores[1])}");

                // Get last enchantment (if it's a Crate Exclusive or different from first two)
                if (armor.LeggingsItem.Lores.Length > 2)
                {
                    string lastLore = null;

                    // Look for Crate Exclusive
                    for (int i = 0; i < armor.LeggingsItem.Lores.Length; i++)
                    {
                        if (armor.LeggingsItem.Lores[i].Contains("Crate Exclusive"))
                        {
                            lastLore = armor.LeggingsItem.Lores[i];
                            break;
                        }
                    }

                    // If no Crate Exclusive found, use the last lore line
                    if (lastLore == null)
                        lastLore = armor.LeggingsItem.Lores[armor.LeggingsItem.Lores.Length - 1];

                    // Only add if different from first two
                    if (armor.LeggingsItem.Lores.Length <= 1 ||
                        (lastLore != armor.LeggingsItem.Lores[0] && lastLore != armor.LeggingsItem.Lores[1]))
                    {
                        formattedEquipment.AppendLine($"> {CleanLoreLine(lastLore)}");
                    }
                }
            }

            formattedEquipment.AppendLine();
        }

        // Format boots
        if (armor.BootsItem != null && !armor.Boots.Contains("Not visible"))
        {
            string itemName = armor.BootsItem.DisplayName;
            if (string.IsNullOrEmpty(itemName))
                itemName = armor.BootsItem.Type.ToString();

            // Clean the item name
            string cleanedItemName = CleanLoreLine(itemName);

            formattedEquipment.AppendLine($"**Boots**: {cleanedItemName}");

            if (armor.BootsItem.Lores != null && armor.BootsItem.Lores.Length > 0)
            {
                // Get first two enchantments
                if (armor.BootsItem.Lores.Length > 0)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.BootsItem.Lores[0])}");

                if (armor.BootsItem.Lores.Length > 1)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.BootsItem.Lores[1])}");

                // Get last enchantment (if it's a Crate Exclusive or different from first two)
                if (armor.BootsItem.Lores.Length > 2)
                {
                    string lastLore = null;

                    // Look for Crate Exclusive
                    for (int i = 0; i < armor.BootsItem.Lores.Length; i++)
                    {
                        if (armor.BootsItem.Lores[i].Contains("Crate Exclusive"))
                        {
                            lastLore = armor.BootsItem.Lores[i];
                            break;
                        }
                    }

                    // If no Crate Exclusive found, use the last lore line
                    if (lastLore == null)
                        lastLore = armor.BootsItem.Lores[armor.BootsItem.Lores.Length - 1];

                    // Only add if different from first two
                    if (armor.BootsItem.Lores.Length <= 1 ||
                        (lastLore != armor.BootsItem.Lores[0] && lastLore != armor.BootsItem.Lores[1]))
                    {
                        formattedEquipment.AppendLine($"> {CleanLoreLine(lastLore)}");
                    }
                }
            }

            formattedEquipment.AppendLine();
        }

        // Format main hand
        if (armor.MainHandItem != null && !armor.MainHand.Contains("Not visible"))
        {
            string itemName = armor.MainHandItem.DisplayName;
            if (string.IsNullOrEmpty(itemName))
                itemName = armor.MainHandItem.Type.ToString();

            // Clean the item name
            string cleanedItemName = CleanLoreLine(itemName);

            formattedEquipment.AppendLine($"**Main Hand**: {cleanedItemName}");

            if (armor.MainHandItem.Lores != null && armor.MainHandItem.Lores.Length > 0)
            {
                // Get first two enchantments
                if (armor.MainHandItem.Lores.Length > 0)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.MainHandItem.Lores[0])}");

                if (armor.MainHandItem.Lores.Length > 1)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.MainHandItem.Lores[1])}");

                // Get last enchantment (if it's a Crate Exclusive or different from first two)
                if (armor.MainHandItem.Lores.Length > 2)
                {
                    string lastLore = null;

                    // Look for Crate Exclusive
                    for (int i = 0; i < armor.MainHandItem.Lores.Length; i++)
                    {
                        if (armor.MainHandItem.Lores[i].Contains("Crate Exclusive"))
                        {
                            lastLore = armor.MainHandItem.Lores[i];
                            break;
                        }
                    }

                    // If no Crate Exclusive found, use the last lore line
                    if (lastLore == null)
                        lastLore = armor.MainHandItem.Lores[armor.MainHandItem.Lores.Length - 1];

                    // Only add if different from first two
                    if (armor.MainHandItem.Lores.Length <= 1 ||
                        (lastLore != armor.MainHandItem.Lores[0] && lastLore != armor.MainHandItem.Lores[1]))
                    {
                        formattedEquipment.AppendLine($"> {CleanLoreLine(lastLore)}");
                    }
                }
            }

            formattedEquipment.AppendLine();
        }

        // Format off hand
        if (armor.OffHandItem != null)
        {
            string itemName = armor.OffHandItem.DisplayName;
            if (string.IsNullOrEmpty(itemName))
                itemName = armor.OffHandItem.Type.ToString();

            // Clean the item name
            string cleanedItemName = CleanLoreLine(itemName);

            formattedEquipment.AppendLine($"**Off Hand**: {cleanedItemName}");

            if (armor.OffHandItem.Lores != null && armor.OffHandItem.Lores.Length > 0)
            {
                // Get first two enchantments
                if (armor.OffHandItem.Lores.Length > 0)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.OffHandItem.Lores[0])}");

                if (armor.OffHandItem.Lores.Length > 1)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.OffHandItem.Lores[1])}");

                // Get last enchantment (if it's a Crate Exclusive or different from first two)
                if (armor.OffHandItem.Lores.Length > 2)
                {
                    string lastLore = null;

                    // Look for Crate Exclusive
                    for (int i = 0; i < armor.OffHandItem.Lores.Length; i++)
                    {
                        if (armor.OffHandItem.Lores[i].Contains("Crate Exclusive"))
                        {
                            lastLore = armor.OffHandItem.Lores[i];
                            break;
                        }
                    }

                    // If no Crate Exclusive found, use the last lore line
                    if (lastLore == null)
                        lastLore = armor.OffHandItem.Lores[armor.OffHandItem.Lores.Length - 1];

                    // Only add if different from first two
                    if (armor.OffHandItem.Lores.Length <= 1 ||
                        (lastLore != armor.OffHandItem.Lores[0] && lastLore != armor.OffHandItem.Lores[1]))
                    {
                        formattedEquipment.AppendLine($"> {CleanLoreLine(lastLore)}");
                    }
                }
            }

            formattedEquipment.AppendLine();
        }

        return formattedEquipment.ToString();
    }

    private string CleanLoreLine(string loreLine)
    {
        if (string.IsNullOrEmpty(loreLine))
            return "";

        // Make a copy of the original for logging
        string original = loreLine;

        // First, remove all Minecraft formatting codes (§ followed by a character)
        string cleaned = System.Text.RegularExpressions.Regex.Replace(loreLine, "§[0-9a-fk-or]", "");

        // Remove the § character itself (in case it's not followed by a valid formatting code)
        cleaned = cleaned.Replace("§", "");

        // Replace special characters with their ASCII equivalents where possible
        cleaned = cleaned.Replace("������", "")  // Remove special characters
                         .Replace("�", "")       // Remove replacement character
                         .Replace("❃", "")       // Remove flower symbol
                         .Replace("◆", "")       // Remove diamond symbol
                         .Replace("[", "")       // Remove brackets
                         .Replace("]", "");      // Remove brackets

        // Remove any remaining non-ASCII characters
        cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"[^\x20-\x7E]", "");

        // Remove any double spaces that might have been created
        while (cleaned.Contains("  "))
            cleaned = cleaned.Replace("  ", " ");

        // Silent cleaning - no logging to reduce spam

        return cleaned.Trim();
    }

    private bool HasVoteArmor(ArmorInfo armor)
    {
        // Log all equipment for debugging
        LogToConsole($"Checking for VOTE equipment:");

        // Check each piece of equipment for "VOTE" in the name
        if (armor.HelmetItem != null &&
            armor.HelmetItem.DisplayName != null)
        {
            LogToConsole($"Helmet: {armor.HelmetItem.DisplayName}");
            if (armor.HelmetItem.DisplayName.ToUpper().Contains("VOTE"))
            {
                LogToConsole($"VOTE HELMET DETECTED!");
                return true;
            }
        }

        if (armor.ChestplateItem != null &&
            armor.ChestplateItem.DisplayName != null)
        {
            LogToConsole($"Chestplate: {armor.ChestplateItem.DisplayName}");
            if (armor.ChestplateItem.DisplayName.ToUpper().Contains("VOTE"))
            {
                LogToConsole($"VOTE CHESTPLATE DETECTED!");
                return true;
            }
        }

        if (armor.LeggingsItem != null &&
            armor.LeggingsItem.DisplayName != null)
        {
            LogToConsole($"Leggings: {armor.LeggingsItem.DisplayName}");
            if (armor.LeggingsItem.DisplayName.ToUpper().Contains("VOTE"))
            {
                LogToConsole($"VOTE LEGGINGS DETECTED!");
                return true;
            }
        }

        if (armor.BootsItem != null &&
            armor.BootsItem.DisplayName != null)
        {
            LogToConsole($"Boots: {armor.BootsItem.DisplayName}");
            if (armor.BootsItem.DisplayName.ToUpper().Contains("VOTE"))
            {
                LogToConsole($"VOTE BOOTS DETECTED!");
                return true;
            }
        }

        if (armor.MainHandItem != null &&
            armor.MainHandItem.DisplayName != null)
        {
            LogToConsole($"Main Hand: {armor.MainHandItem.DisplayName}");
            if (armor.MainHandItem.DisplayName.ToUpper().Contains("VOTE"))
            {
                LogToConsole($"VOTE MAIN HAND DETECTED!");
                return true;
            }
        }

        if (armor.OffHandItem != null &&
            armor.OffHandItem.DisplayName != null)
        {
            LogToConsole($"Off Hand: {armor.OffHandItem.DisplayName}");
            if (armor.OffHandItem.DisplayName.ToUpper().Contains("VOTE"))
            {
                LogToConsole($"VOTE OFF HAND DETECTED!");
                return true;
            }
        }

        // Check if any piece of armor has "VOTE" in the name
        if ((armor.Helmet != null && armor.Helmet.ToUpper().Contains("VOTE")) ||
            (armor.Chestplate != null && armor.Chestplate.ToUpper().Contains("VOTE")) ||
            (armor.Leggings != null && armor.Leggings.ToUpper().Contains("VOTE")) ||
            (armor.Boots != null && armor.Boots.ToUpper().Contains("VOTE")) ||
            (armor.MainHand != null && armor.MainHand.ToUpper().Contains("VOTE")) ||
            (armor.OffHand != null && armor.OffHand.ToUpper().Contains("VOTE")))
        {
            LogToConsole($"VOTE EQUIPMENT DETECTED IN STRING PROPERTIES!");
            return true;
        }

        return false;
    }

    private bool HasGladiatorArmor(ArmorInfo armor)
    {
        LogToConsole("NUCLEAR GLADIATOR CHECK:");

        // Check each piece individually with multiple methods
        if (armor.Helmet != null)
        {
            LogToConsole($"Checking Helmet: '{armor.Helmet}'");
            if (armor.Helmet.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN HELMET!");
                return true;
            }
        }

        if (armor.Chestplate != null)
        {
            LogToConsole($"Checking Chestplate: '{armor.Chestplate}'");
            if (armor.Chestplate.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN CHESTPLATE!");
                return true;
            }
        }

        if (armor.Leggings != null)
        {
            LogToConsole($"Checking Leggings: '{armor.Leggings}'");
            if (armor.Leggings.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN LEGGINGS!");
                return true;
            }
        }

        if (armor.Boots != null)
        {
            LogToConsole($"Checking Boots: '{armor.Boots}'");
            if (armor.Boots.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN BOOTS!");
                return true;
            }
        }

        if (armor.MainHand != null)
        {
            LogToConsole($"Checking MainHand: '{armor.MainHand}'");
            if (armor.MainHand.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN MAIN HAND!");
                return true;
            }
        }

        if (armor.OffHand != null)
        {
            LogToConsole($"Checking OffHand: '{armor.OffHand}'");
            if (armor.OffHand.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN OFF HAND!");
                return true;
            }
        }

        // Check DisplayName properties
        if (armor.HelmetItem != null && armor.HelmetItem.DisplayName != null)
        {
            LogToConsole($"Checking HelmetItem.DisplayName: '{armor.HelmetItem.DisplayName}'");
            if (armor.HelmetItem.DisplayName.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN HELMET DISPLAY NAME!");
                return true;
            }
        }

        if (armor.ChestplateItem != null && armor.ChestplateItem.DisplayName != null)
        {
            LogToConsole($"Checking ChestplateItem.DisplayName: '{armor.ChestplateItem.DisplayName}'");
            if (armor.ChestplateItem.DisplayName.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN CHESTPLATE DISPLAY NAME!");
                return true;
            }
        }

        if (armor.LeggingsItem != null && armor.LeggingsItem.DisplayName != null)
        {
            LogToConsole($"Checking LeggingsItem.DisplayName: '{armor.LeggingsItem.DisplayName}'");
            if (armor.LeggingsItem.DisplayName.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN LEGGINGS DISPLAY NAME!");
                return true;
            }
        }

        if (armor.BootsItem != null && armor.BootsItem.DisplayName != null)
        {
            LogToConsole($"Checking BootsItem.DisplayName: '{armor.BootsItem.DisplayName}'");
            if (armor.BootsItem.DisplayName.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN BOOTS DISPLAY NAME!");
                return true;
            }
        }

        if (armor.MainHandItem != null && armor.MainHandItem.DisplayName != null)
        {
            LogToConsole($"Checking MainHandItem.DisplayName: '{armor.MainHandItem.DisplayName}'");
            if (armor.MainHandItem.DisplayName.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN MAIN HAND DISPLAY NAME!");
                return true;
            }
        }

        if (armor.OffHandItem != null && armor.OffHandItem.DisplayName != null)
        {
            LogToConsole($"Checking OffHandItem.DisplayName: '{armor.OffHandItem.DisplayName}'");
            if (armor.OffHandItem.DisplayName.ToLower().Contains("gladiator"))
            {
                LogToConsole("NUCLEAR GLADIATOR DETECTED IN OFF HAND DISPLAY NAME!");
                return true;
            }
        }

        // Combined check
        string allEquipment =
            (armor.Helmet ?? "").ToLower() +
            (armor.Chestplate ?? "").ToLower() +
            (armor.Leggings ?? "").ToLower() +
            (armor.Boots ?? "").ToLower() +
            (armor.MainHand ?? "").ToLower() +
            (armor.OffHand ?? "").ToLower();

        LogToConsole($"Combined equipment string: '{allEquipment}'");

        if (allEquipment.Contains("gladiator"))
        {
            LogToConsole("NUCLEAR GLADIATOR DETECTED IN COMBINED STRING!");
            return true;
        }

        LogToConsole("NO GLADIATOR EQUIPMENT DETECTED!");
        return false;
    }

    private bool HasFeatherArmor(ArmorInfo armor)
    {
        // Check each piece of equipment for "Feather" in the name
        if (armor.HelmetItem != null &&
            armor.HelmetItem.DisplayName != null &&
            armor.HelmetItem.DisplayName.Contains("Feather"))
            return true;

        if (armor.ChestplateItem != null &&
            armor.ChestplateItem.DisplayName != null &&
            armor.ChestplateItem.DisplayName.Contains("Feather"))
            return true;

        if (armor.LeggingsItem != null &&
            armor.LeggingsItem.DisplayName != null &&
            armor.LeggingsItem.DisplayName.Contains("Feather"))
            return true;

        if (armor.BootsItem != null &&
            armor.BootsItem.DisplayName != null &&
            armor.BootsItem.DisplayName.Contains("Feather"))
            return true;

        if (armor.MainHandItem != null &&
            armor.MainHandItem.DisplayName != null &&
            armor.MainHandItem.DisplayName.Contains("Feather"))
            return true;

        if (armor.OffHandItem != null &&
            armor.OffHandItem.DisplayName != null &&
            armor.OffHandItem.DisplayName.Contains("Feather"))
            return true;

        // Check if any piece of armor has "Feather" in the name
        if ((armor.Helmet != null && armor.Helmet.Contains("Feather")) ||
            (armor.Chestplate != null && armor.Chestplate.Contains("Feather")) ||
            (armor.Leggings != null && armor.Leggings.Contains("Feather")) ||
            (armor.Boots != null && armor.Boots.Contains("Feather")) ||
            (armor.MainHand != null && armor.MainHand.Contains("Feather")) ||
            (armor.OffHand != null && armor.OffHand.Contains("Feather")))
            return true;

        return false;
    }

    private bool HasStarterEquipment(ArmorInfo armor)
    {
        // Check each piece of equipment for "Starter" in the name
        if (armor.HelmetItem != null &&
            armor.HelmetItem.DisplayName != null &&
            armor.HelmetItem.DisplayName.Contains("Starter"))
            return true;

        if (armor.ChestplateItem != null &&
            armor.ChestplateItem.DisplayName != null &&
            armor.ChestplateItem.DisplayName.Contains("Starter"))
            return true;

        if (armor.LeggingsItem != null &&
            armor.LeggingsItem.DisplayName != null &&
            armor.LeggingsItem.DisplayName.Contains("Starter"))
            return true;

        if (armor.BootsItem != null &&
            armor.BootsItem.DisplayName != null &&
            armor.BootsItem.DisplayName.Contains("Starter"))
            return true;

        if (armor.MainHandItem != null &&
            armor.MainHandItem.DisplayName != null &&
            armor.MainHandItem.DisplayName.Contains("Starter"))
            return true;

        if (armor.OffHandItem != null &&
            armor.OffHandItem.DisplayName != null &&
            armor.OffHandItem.DisplayName.Contains("Starter"))
            return true;

        // Check if any piece of armor has "Starter" in the name
        if ((armor.Helmet != null && armor.Helmet.Contains("Starter")) ||
            (armor.Chestplate != null && armor.Chestplate.Contains("Starter")) ||
            (armor.Leggings != null && armor.Leggings.Contains("Starter")) ||
            (armor.Boots != null && armor.Boots.Contains("Starter")) ||
            (armor.MainHand != null && armor.MainHand.Contains("Starter")) ||
            (armor.OffHand != null && armor.OffHand.Contains("Starter")))
            return true;

        return false;
    }

    private bool HasGodlikeEquipment(ArmorInfo armor)
    {
        // Check each piece of equipment for "Godlike" in the name
        if (armor.HelmetItem != null &&
            armor.HelmetItem.DisplayName != null &&
            armor.HelmetItem.DisplayName.Contains("Godlike"))
            return true;

        if (armor.ChestplateItem != null &&
            armor.ChestplateItem.DisplayName != null &&
            armor.ChestplateItem.DisplayName.Contains("Godlike"))
            return true;

        if (armor.LeggingsItem != null &&
            armor.LeggingsItem.DisplayName != null &&
            armor.LeggingsItem.DisplayName.Contains("Godlike"))
            return true;

        if (armor.BootsItem != null &&
            armor.BootsItem.DisplayName != null &&
            armor.BootsItem.DisplayName.Contains("Godlike"))
            return true;

        if (armor.MainHandItem != null &&
            armor.MainHandItem.DisplayName != null &&
            armor.MainHandItem.DisplayName.Contains("Godlike"))
            return true;

        if (armor.OffHandItem != null &&
            armor.OffHandItem.DisplayName != null &&
            armor.OffHandItem.DisplayName.Contains("Godlike"))
            return true;

        return false;
    }

    private bool HasNetheriteOrHighTier(ArmorInfo armor)
    {
        LogToConsole("EQUIPMENT CHECK - CHECKING FOR EXCLUDED TYPES FIRST:");

        // FIRST PRIORITY: Check for Gladiator equipment in display names
        // This is the most reliable way to detect Gladiator equipment
        // Check for both "Gladiator" and "Gladiato" (in case 'r' gets filtered out)
        if (armor.Helmet != null &&
            (armor.Helmet.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.Helmet.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator in Helmet display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Chestplate != null &&
            (armor.Chestplate.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.Chestplate.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator in Chestplate display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Leggings != null &&
            (armor.Leggings.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.Leggings.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator in Leggings display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Boots != null &&
            (armor.Boots.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.Boots.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator in Boots display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.MainHand != null &&
            (armor.MainHand.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.MainHand.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator in MainHand display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.OffHand != null &&
            (armor.OffHand.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.OffHand.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator in OffHand display name. BLOCKING WEBHOOK.");
            return false;
        }

        // Check for Luna/Lunar equipment
        if (armor.Helmet != null &&
            (armor.Helmet.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Luna/Lunar in Helmet display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Chestplate != null &&
            (armor.Chestplate.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Luna/Lunar in Chestplate display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Leggings != null &&
            (armor.Leggings.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Luna/Lunar in Leggings display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Boots != null &&
            (armor.Boots.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Luna/Lunar in Boots display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.MainHand != null &&
            (armor.MainHand.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Luna/Lunar in MainHand display name. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.OffHand != null &&
            (armor.OffHand.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Luna/Lunar in OffHand display name. BLOCKING WEBHOOK.");
            return false;
        }

        // Additional check for Gladiator equipment using combined string
        string allEquipment =
            (armor.Helmet ?? "").ToLower() +
            (armor.Chestplate ?? "").ToLower() +
            (armor.Leggings ?? "").ToLower() +
            (armor.Boots ?? "").ToLower() +
            (armor.MainHand ?? "").ToLower() +
            (armor.OffHand ?? "").ToLower();

        LogToConsole($"Combined equipment string for additional checks: '{allEquipment}'");

        // Check for Gladiator/Gladiato/Glad in combined string
        if (allEquipment.Contains("gladiator") ||
            allEquipment.Contains("gladiato") ||
            allEquipment.Contains("glad ") ||  // Space after to avoid matching other words
            allEquipment.Contains(" glad"))    // Space before to avoid matching other words
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator/Glad in combined equipment string. BLOCKING WEBHOOK.");
            return false;
        }

        // Check for Luna/Lunar in combined string
        if (allEquipment.Contains("luna"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Luna/Lunar in combined equipment string. BLOCKING WEBHOOK.");
            return false;
        }

        // SECOND PRIORITY: Check for VOTE equipment
        if (armor.Helmet != null && armor.Helmet.ToUpper().Contains("VOTE"))
        {
            LogToConsole($"CRITICAL BLOCK: Found VOTE in Helmet. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Chestplate != null && armor.Chestplate.ToUpper().Contains("VOTE"))
        {
            LogToConsole($"CRITICAL BLOCK: Found VOTE in Chestplate. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Leggings != null && armor.Leggings.ToUpper().Contains("VOTE"))
        {
            LogToConsole($"CRITICAL BLOCK: Found VOTE in Leggings. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Boots != null && armor.Boots.ToUpper().Contains("VOTE"))
        {
            LogToConsole($"CRITICAL BLOCK: Found VOTE in Boots. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.MainHand != null && armor.MainHand.ToUpper().Contains("VOTE"))
        {
            LogToConsole($"CRITICAL BLOCK: Found VOTE in MainHand. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.OffHand != null && armor.OffHand.ToUpper().Contains("VOTE"))
        {
            LogToConsole($"CRITICAL BLOCK: Found VOTE in OffHand. BLOCKING WEBHOOK.");
            return false;
        }

        // THIRD PRIORITY: Check for Feather equipment
        if (armor.Helmet != null && armor.Helmet.Contains("Feather"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Feather in Helmet. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Chestplate != null && armor.Chestplate.Contains("Feather"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Feather in Chestplate. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Leggings != null && armor.Leggings.Contains("Feather"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Feather in Leggings. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Boots != null && armor.Boots.Contains("Feather"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Feather in Boots. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.MainHand != null && armor.MainHand.Contains("Feather"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Feather in MainHand. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.OffHand != null && armor.OffHand.Contains("Feather"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Feather in OffHand. BLOCKING WEBHOOK.");
            return false;
        }

        // FOURTH PRIORITY: Check for Starter equipment
        if (armor.Helmet != null && armor.Helmet.Contains("Starter"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Starter in Helmet. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Chestplate != null && armor.Chestplate.Contains("Starter"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Starter in Chestplate. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Leggings != null && armor.Leggings.Contains("Starter"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Starter in Leggings. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.Boots != null && armor.Boots.Contains("Starter"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Starter in Boots. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.MainHand != null && armor.MainHand.Contains("Starter"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Starter in MainHand. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.OffHand != null && armor.OffHand.Contains("Starter"))
        {
            LogToConsole($"CRITICAL BLOCK: Found Starter in OffHand. BLOCKING WEBHOOK.");
            return false;
        }

        // Log all equipment types for debugging
        LogToConsole("DETAILED EQUIPMENT CHECK:");

        // DIRECT CHECK FOR EXCLUDED EQUIPMENT TYPES - HIGHEST PRIORITY CHECK
        // Check for Gladiator equipment in any slot - this is a complete block regardless of material
        if ((armor.Helmet != null && (armor.Helmet.ToLower().Contains("gladiator") || armor.Helmet.ToLower().Contains("gladiato"))) ||
            (armor.Chestplate != null && (armor.Chestplate.ToLower().Contains("gladiator") || armor.Chestplate.ToLower().Contains("gladiato"))) ||
            (armor.Leggings != null && (armor.Leggings.ToLower().Contains("gladiator") || armor.Leggings.ToLower().Contains("gladiato"))) ||
            (armor.Boots != null && (armor.Boots.ToLower().Contains("gladiator") || armor.Boots.ToLower().Contains("gladiato"))) ||
            (armor.MainHand != null && (armor.MainHand.ToLower().Contains("gladiator") || armor.MainHand.ToLower().Contains("gladiato"))) ||
            (armor.OffHand != null && (armor.OffHand.ToLower().Contains("gladiator") || armor.OffHand.ToLower().Contains("gladiato"))))
        {
            LogToConsole($"ABSOLUTE BLOCK: Found Gladiator equipment. Skipping webhook regardless of material.");
            return false;
        }

        // Check for Luna/Lunar equipment in any slot
        if ((armor.Helmet != null && armor.Helmet.ToLower().Contains("luna")) ||
            (armor.Chestplate != null && armor.Chestplate.ToLower().Contains("luna")) ||
            (armor.Leggings != null && armor.Leggings.ToLower().Contains("luna")) ||
            (armor.Boots != null && armor.Boots.ToLower().Contains("luna")) ||
            (armor.MainHand != null && armor.MainHand.ToLower().Contains("luna")) ||
            (armor.OffHand != null && armor.OffHand.ToLower().Contains("luna")))
        {
            LogToConsole($"ABSOLUTE BLOCK: Found Luna/Lunar equipment. Skipping webhook regardless of material.");
            return false;
        }

        // Check for VOTE equipment in any slot
        if ((armor.Helmet != null && armor.Helmet.ToUpper().Contains("VOTE")) ||
            (armor.Chestplate != null && armor.Chestplate.ToUpper().Contains("VOTE")) ||
            (armor.Leggings != null && armor.Leggings.ToUpper().Contains("VOTE")) ||
            (armor.Boots != null && armor.Boots.ToUpper().Contains("VOTE")) ||
            (armor.MainHand != null && armor.MainHand.ToUpper().Contains("VOTE")) ||
            (armor.OffHand != null && armor.OffHand.ToUpper().Contains("VOTE")))
        {
            LogToConsole($"ABSOLUTE BLOCK: Found VOTE equipment. Skipping webhook regardless of material.");
            return false;
        }

        // Check for Feather equipment in any slot
        if ((armor.Helmet != null && armor.Helmet.Contains("Feather")) ||
            (armor.Chestplate != null && armor.Chestplate.Contains("Feather")) ||
            (armor.Leggings != null && armor.Leggings.Contains("Feather")) ||
            (armor.Boots != null && armor.Boots.Contains("Feather")) ||
            (armor.MainHand != null && armor.MainHand.Contains("Feather")) ||
            (armor.OffHand != null && armor.OffHand.Contains("Feather")))
        {
            LogToConsole($"ABSOLUTE BLOCK: Found Feather equipment. Skipping webhook regardless of material.");
            return false;
        }

        // Check for Starter equipment in any slot
        if ((armor.Helmet != null && armor.Helmet.Contains("Starter")) ||
            (armor.Chestplate != null && armor.Chestplate.Contains("Starter")) ||
            (armor.Leggings != null && armor.Leggings.Contains("Starter")) ||
            (armor.Boots != null && armor.Boots.Contains("Starter")) ||
            (armor.MainHand != null && armor.MainHand.Contains("Starter")) ||
            (armor.OffHand != null && armor.OffHand.Contains("Starter")))
        {
            LogToConsole($"ABSOLUTE BLOCK: Found Starter equipment. Skipping webhook regardless of material.");
            return false;
        }

        // Check item types for Gladiator equipment
        // Sometimes Gladiator equipment has NetheriteHelmet/NetheriteChestplate/NetheriteLeggings as its Type
        // but "Gladiator" in its DisplayName
        if (armor.HelmetItem != null && armor.HelmetItem.DisplayName != null &&
            (armor.HelmetItem.DisplayName.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.HelmetItem.DisplayName.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.HelmetItem.DisplayName.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator/Luna in Helmet DisplayName. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.ChestplateItem != null && armor.ChestplateItem.DisplayName != null &&
            (armor.ChestplateItem.DisplayName.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.ChestplateItem.DisplayName.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.ChestplateItem.DisplayName.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator/Luna in Chestplate DisplayName. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.LeggingsItem != null && armor.LeggingsItem.DisplayName != null &&
            (armor.LeggingsItem.DisplayName.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.LeggingsItem.DisplayName.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.LeggingsItem.DisplayName.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator/Luna in Leggings DisplayName. BLOCKING WEBHOOK.");
            return false;
        }

        if (armor.BootsItem != null && armor.BootsItem.DisplayName != null &&
            (armor.BootsItem.DisplayName.IndexOf("Gladiator", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.BootsItem.DisplayName.IndexOf("Gladiato", StringComparison.OrdinalIgnoreCase) >= 0 ||
             armor.BootsItem.DisplayName.IndexOf("Luna", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            LogToConsole($"CRITICAL BLOCK: Found Gladiator/Luna in Boots DisplayName. BLOCKING WEBHOOK.");
            return false;
        }

        // Log all equipment types for debugging
        LogToConsole("DETAILED EQUIPMENT CHECK:");
        if (armor.HelmetItem != null)
        {
            string itemType = armor.HelmetItem.Type.ToString() ?? "Unknown";
            LogToConsole($"Helmet Type: {itemType}, Name: {armor.Helmet}");
        }
        else
        {
            LogToConsole($"Helmet Type: None, Name: {armor.Helmet}");
        }

        if (armor.ChestplateItem != null)
        {
            string itemType = armor.ChestplateItem.Type.ToString() ?? "Unknown";
            LogToConsole($"Chestplate Type: {itemType}, Name: {armor.Chestplate}");
        }
        else
        {
            LogToConsole($"Chestplate Type: None, Name: {armor.Chestplate}");
        }

        if (armor.LeggingsItem != null)
        {
            string itemType = armor.LeggingsItem.Type.ToString() ?? "Unknown";
            LogToConsole($"Leggings Type: {itemType}, Name: {armor.Leggings}");
        }
        else
        {
            LogToConsole($"Leggings Type: None, Name: {armor.Leggings}");
        }

        if (armor.BootsItem != null)
        {
            string itemType = armor.BootsItem.Type.ToString() ?? "Unknown";
            LogToConsole($"Boots Type: {itemType}, Name: {armor.Boots}");
        }
        else
        {
            LogToConsole($"Boots Type: None, Name: {armor.Boots}");
        }

        if (armor.MainHandItem != null)
        {
            string itemType = armor.MainHandItem.Type.ToString() ?? "Unknown";
            LogToConsole($"MainHand Type: {itemType}, Name: {armor.MainHand}");
        }
        else
        {
            LogToConsole($"MainHand Type: None, Name: {armor.MainHand}");
        }

        if (armor.OffHandItem != null)
        {
            string itemType = armor.OffHandItem.Type.ToString() ?? "Unknown";
            LogToConsole($"OffHand Type: {itemType}, Name: {armor.OffHand}");
        }
        else
        {
            LogToConsole($"OffHand Type: None, Name: {armor.OffHand}");
        }

        // Check for Godlike equipment FIRST as a special case
        if ((armor.Helmet != null && armor.Helmet.Contains("Godlike")) ||
            (armor.Chestplate != null && armor.Chestplate.Contains("Godlike")) ||
            (armor.Leggings != null && armor.Leggings.Contains("Godlike")) ||
            (armor.Boots != null && armor.Boots.Contains("Godlike")) ||
            (armor.MainHand != null && armor.MainHand.Contains("Godlike")) ||
            (armor.OffHand != null && armor.OffHand.Contains("Godlike")))
        {
            LogToConsole("Player has Godlike equipment. Sending webhook.");
            return true;
        }

        // Count how many pieces of netherite armor the player has
        int netheriteCount = 0;

        // Check if any piece is Netherite (check both name and type)
        if ((armor.Helmet != null && armor.Helmet.ToLower().Contains("netherite")) ||
            (armor.HelmetItem != null && armor.HelmetItem.Type.ToString().ToLower().Contains("netherite")))
        {
            LogToConsole($"Found Netherite Helmet");
            netheriteCount++;
        }

        if ((armor.Chestplate != null && armor.Chestplate.ToLower().Contains("netherite")) ||
            (armor.ChestplateItem != null && armor.ChestplateItem.Type.ToString().ToLower().Contains("netherite")))
        {
            LogToConsole($"Found Netherite Chestplate");
            netheriteCount++;
        }

        if ((armor.Leggings != null && armor.Leggings.ToLower().Contains("netherite")) ||
            (armor.LeggingsItem != null && armor.LeggingsItem.Type.ToString().ToLower().Contains("netherite")))
        {
            LogToConsole($"Found Netherite Leggings");
            netheriteCount++;
        }

        if ((armor.Boots != null && armor.Boots.ToLower().Contains("netherite")) ||
            (armor.BootsItem != null && armor.BootsItem.Type.ToString().ToLower().Contains("netherite")))
        {
            LogToConsole($"Found Netherite Boots");
            netheriteCount++;
        }

        // Only send webhook if player has at least 3 pieces of netherite armor
        if (netheriteCount >= 3)
        {
            LogToConsole($"Player has {netheriteCount} pieces of Netherite armor. Sending webhook.");
            return true;
        }

        LogToConsole("Player does not have enough Netherite armor or Godlike equipment. Skipping webhook.");
        return false;
    }

    private void SendToDiscordViaFile(string playerName, Location location, ArmorInfo armor)
    {
        try
        {
            // ABSOLUTE BLOCK FOR MONSTER PLAYERS
            if (playerName.ToLower().Contains("monster"))
            {
                LogToConsole($"ABSOLUTE BLOCK: Player {playerName} is a monster. BLOCKING WEBHOOK.");
                return;
            }

            // Check if the player has netherite or high-tier equipment
            if (!HasNetheriteOrHighTier(armor))
            {
                LogToConsole($"Player {playerName} does not have netherite or high-tier equipment. Skipping webhook.");
                return;
            }

            // Check if player is in the blacklist
            if (playerBlacklist.Contains(playerName))
            {
                LogToConsole($"Player {playerName} is in the blacklist. Skipping webhook.");
                return;
            }
            // Log equipment for debugging purposes only
            LogToConsole("EQUIPMENT CHECK IN SENDTODISCORDVIAFILE:");
            if (armor.Helmet != null)
                LogToConsole($"Helmet: {armor.Helmet}");
            if (armor.Chestplate != null)
                LogToConsole($"Chestplate: {armor.Chestplate}");
            if (armor.Leggings != null)
                LogToConsole($"Leggings: {armor.Leggings}");
            if (armor.Boots != null)
                LogToConsole($"Boots: {armor.Boots}");
            if (armor.MainHand != null)
                LogToConsole($"Main Hand: {armor.MainHand}");
            if (armor.OffHand != null)
                LogToConsole($"Off Hand: {armor.OffHand}");



            // Create a temporary file with the webhook data
            string webhookDataFile = "discord_webhook_data.txt";

            // All equipment checks are now handled by HasNetheriteOrHighTier

            // Format the equipment for Discord
            string formattedEquipment = FormatEquipmentForDiscord(armor);

            // Silent formatting - no logging to reduce spam

            // Create the webhook data
            List<string> webhookData = new List<string>();
            webhookData.Add($"WEBHOOK_URL={discordWebhookUrl}");
            webhookData.Add($"PLAYER_NAME={playerName}");
            webhookData.Add("FORMATTED_EQUIPMENT=" + formattedEquipment);

            // Write the webhook data to a file
            File.WriteAllLines(webhookDataFile, webhookData);

            // Execute the Python script
            System.Diagnostics.Process process = new System.Diagnostics.Process();
            process.StartInfo.FileName = "python";
            process.StartInfo.Arguments = "send_webhook.py";
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.CreateNoWindow = true;
            process.Start();

            LogToConsole($"Sending Discord webhook for player {playerName}...");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error sending Discord webhook: {ex.Message}");
        }
    }

    private void OnPlayerLeftArea(string playerName, PlayerInfo playerInfo)
    {
        TimeSpan timeInArea = DateTime.Now - playerInfo.EnteredTime;
        string message = $"Player {playerName} left the box area after {timeInArea.TotalMinutes:F1} minutes";
        LogToConsole(message);

        // Log to file
        if (logToFile)
        {
            try
            {
                File.AppendAllText(logFilePath,
                    $"[{DateTime.Now}] LEFT: {playerName} after {timeInArea.TotalMinutes:F1} minutes in area\n");
            }
            catch { /* Ignore file write errors */ }
        }
    }

    // Handle chat messages for additional monitoring
    public override void GetText(string text)
    {
        // Get the raw text without formatting
        string message = GetVerbatim(text);

        // Look for chat messages from players in the monitored area
        foreach (string playerName in playersInArea.Keys)
        {
            if (message.Contains($"<{playerName}>"))
            {
                LogToConsole($"Player in box area said: {message}");

                // Log to file
                if (logToFile)
                {
                    try
                    {
                        File.AppendAllText(logFilePath,
                            $"[{DateTime.Now}] CHAT: {message}\n");
                    }
                    catch { /* Ignore file write errors */ }
                }
            }
        }

        // Look for commands from the bot owner
        string username = "";
        string command = "";

        if (IsPrivateMessage(text, ref command, ref username) && username == GetUsername())
        {
            // Process commands from the bot owner
            if (command.StartsWith("!box"))
            {
                string[] parts = command.Split(' ');

                if (parts.Length > 1)
                {
                    switch (parts[1].ToLower())
                    {
                        case "list":
                            // List players in area
                            if (playersInArea.Count == 0)
                            {
                                SendPrivateMessage(username, "No players currently in the box area.");
                            }
                            else
                            {
                                SendPrivateMessage(username, $"{playersInArea.Count} players in box area:");
                                foreach (var player in playersInArea)
                                {
                                    TimeSpan timeInArea = DateTime.Now - player.Value.EnteredTime;
                                    SendPrivateMessage(username, $"- {player.Key} (for {timeInArea.TotalMinutes:F1} minutes)");
                                    SendPrivateMessage(username, $"  Armor: {GetArmorSummary(player.Value.ArmorInfo)}");
                                }
                            }
                            break;

                        case "cooldown":
                            // Adjust cooldown time
                            if (parts.Length > 2 && int.TryParse(parts[2], out int newCooldown) && newCooldown > 0)
                            {
                                detectionCooldown = newCooldown;
                                SendPrivateMessage(username, $"Detection cooldown set to {detectionCooldown} seconds.");
                            }
                            else
                            {
                                SendPrivateMessage(username, $"Current detection cooldown is {detectionCooldown} seconds.");
                                SendPrivateMessage(username, "Use '!box cooldown <seconds>' to change it.");
                            }
                            break;

                        case "sound":
                            // Toggle sound alerts
                            if (parts.Length > 2)
                            {
                                if (parts[2].ToLower() == "on")
                                {
                                    enableSoundAlerts = true;
                                    SendPrivateMessage(username, "Sound alerts enabled.");
                                }
                                else if (parts[2].ToLower() == "off")
                                {
                                    enableSoundAlerts = false;
                                    SendPrivateMessage(username, "Sound alerts disabled.");
                                }
                            }
                            break;

                        case "config":
                            // Reload or save configuration
                            if (parts.Length > 2)
                            {
                                if (parts[2].ToLower() == "reload")
                                {
                                    LoadConfig();
                                    SendPrivateMessage(username, "Configuration reloaded from file.");
                                }
                                else if (parts[2].ToLower() == "save")
                                {
                                    SaveConfig();
                                    SendPrivateMessage(username, "Configuration saved to file.");
                                }
                            }
                            else
                            {
                                SendPrivateMessage(username, $"Discord notifications: {(sendToDiscord ? "Enabled" : "Disabled")}");
                                SendPrivateMessage(username, $"Sound alerts: {(enableSoundAlerts ? "Enabled" : "Disabled")}");
                                SendPrivateMessage(username, $"Log to file: {(logToFile ? "Enabled" : "Disabled")}");
                                SendPrivateMessage(username, $"Detection cooldown: {detectionCooldown} seconds");
                                SendPrivateMessage(username, "Edit the config file to change these settings.");
                                SendPrivateMessage(username, "Use '!box config reload' to reload the configuration.");
                            }
                            break;

                        case "nearby":
                            // List players in extended area
                            if (playersNearby.Count == 0)
                            {
                                SendPrivateMessage(username, "No players currently in the extended monitoring area.");
                            }
                            else
                            {
                                SendPrivateMessage(username, $"{playersNearby.Count} players in extended monitoring area:");
                                foreach (var player in playersNearby)
                                {
                                    TimeSpan timeSinceLastSeen = DateTime.Now - player.Value.LastEquipmentCheck;
                                    SendPrivateMessage(username, $"- {player.Key} (last seen {timeSinceLastSeen.TotalMinutes:F1} minutes ago)");
                                    SendPrivateMessage(username, $"  Armor: {GetArmorSummary(player.Value.ArmorInfo)}");
                                }
                            }
                            break;

                        case "help":
                            // Show help
                            SendPrivateMessage(username, "Box Area Monitor Commands:");
                            SendPrivateMessage(username, "!box list - List players in box area");
                            SendPrivateMessage(username, "!box nearby - List players in extended monitoring area");
                            SendPrivateMessage(username, "!box cooldown <seconds> - Set detection cooldown");
                            SendPrivateMessage(username, "!box sound on/off - Toggle sound alerts");
                            SendPrivateMessage(username, "!box config - Show current configuration");
                            SendPrivateMessage(username, "!box config reload - Reload configuration from file");
                            SendPrivateMessage(username, "!box config save - Save current settings to file");
                            SendPrivateMessage(username, "!box help - Show this help");
                            break;
                    }
                }
                else
                {
                    // Just !box command with no arguments
                    SendPrivateMessage(username, $"Monitoring box area with {playersInArea.Count} players inside.");
                    SendPrivateMessage(username, $"Tracking {playersNearby.Count} players in extended monitoring area.");
                    SendPrivateMessage(username, $"Detection cooldown: {detectionCooldown} seconds");
                    SendPrivateMessage(username, "Use '!box help' for commands.");
                }
            }
        }
    }

    // Helper classes to store player information
    public class PlayerInfo
    {
        public DateTime EnteredTime { get; set; }
        public Location LastPosition { get; set; }
        public ArmorInfo ArmorInfo { get; set; }
        public bool EquipmentChecked { get; set; } = false;
        public DateTime LastEquipmentCheck { get; set; } = DateTime.MinValue;
    }

    public class ArmorInfo
    {
        public string Helmet { get; set; } = "None";
        public string Chestplate { get; set; } = "None";
        public string Leggings { get; set; } = "None";
        public string Boots { get; set; } = "None";
        public string MainHand { get; set; } = "None";
        public string OffHand { get; set; } = "None";
        public int ArmorRating { get; set; } = 0;

        // Store the actual items for full lore
        public Item HelmetItem { get; set; } = null;
        public Item ChestplateItem { get; set; } = null;
        public Item LeggingsItem { get; set; } = null;
        public Item BootsItem { get; set; } = null;
        public Item MainHandItem { get; set; } = null;
        public Item OffHandItem { get; set; } = null;
    }
}